<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Teacher Program Diary - Dates & Calendar</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // This configuration is for Tailwind's JIT engine if the CDN script supports it.
    // If `tailwind` object is not defined here due to CDN load failure,
    // the script might error out. The functions below are made more resilient.
    if (typeof tailwind !== 'undefined') {
      tailwind.config = {
        darkMode: 'class', // or 'media'
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            colors: {
              primary: {
                DEFAULT: '#3b82f6', // blue-500
                hover: '#2563eb', // blue-600
                dark: '#60a5fa', // blue-400
              },
              secondary: '#8b5cf6', // violet-500
              tertiary: '#10b981', // emerald-500
              // These are intended for JS access if tailwind object is available
              calendarHighlightColor: '#fde68a', // amber-200 
              darkCalendarHighlightColor: '#7c2d12', // amber-900
            }
          }
        }
      }
    } else {
      console.warn("[Tailwind Init] Tailwind object not defined at config time. Ensure CDN is loading or check for network issues.");
    }
  </script>
  <style>
    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    .dark ::-webkit-scrollbar-track {
      background: #2d3748; /* gray-800 */
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
    .dark ::-webkit-scrollbar-thumb {
      background: #555;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    .dark ::-webkit-scrollbar-thumb:hover {
      background: #777;
    }
    /* Basic transition for dark mode */
    body, .bg-card {
        transition: background-color 0.3s ease, color 0.3s ease;
    }
    .selector-section:not(:first-child):not(.always-active) {
        opacity: 0.5;
        pointer-events: none;
    }
    .selector-section.active {
        opacity: 1;
        pointer-events: auto;
    }
    /* Calendar Styles */
    .calendar-table {
        width: 100%;
        border-collapse: collapse;
    }
    .calendar-table th, .calendar-table td {
        border: 1px solid #e5e7eb; /* gray-200 */
        padding: 0.75rem; /* p-3 */
        text-align: center;
        height: 5rem; /* Approx for content fitting */
        vertical-align: top;
    }
    .dark .calendar-table th, .dark .calendar-table td {
        border-color: #4b5563; /* gray-600 */
    }
    .calendar-table td.other-month {
        color: #9ca3af; /* gray-400 */
        background-color: #f9fafb; /* gray-50 */
    }
    .dark .calendar-table td.other-month {
        color: #6b7280; /* gray-500 */
        background-color: #374151; /* gray-700 */
    }
    .calendar-table td.current-day {
        background-color: #bfdbfe; /* blue-200 */
        font-weight: bold;
    }
    .dark .calendar-table td.current-day {
        background-color: #3730a3; /* indigo-800 */
    }
    .calendar-table td.has-entries {
        /* Uses CSS variable set by JS, with a fallback */
        background-color: var(--calendar-highlight-color, #fde68a); 
        cursor: pointer;
    }
     .calendar-table td.has-entries:hover {
        filter: brightness(95%);
    }
    .dark .calendar-table td.has-entries:hover {
        filter: brightness(120%); /* Make it slightly lighter in dark mode on hover */
    }
    .calendar-day-number {
        font-size: 0.875rem; /* text-sm */
    }
    .calendar-entry-indicator {
        display: block;
        font-size: 0.75rem; /* text-xs */
        margin-top: 0.25rem;
        color: #1e40af; /* blue-800 */
    }
    .dark .calendar-entry-indicator {
        color: #93c5fd; /* blue-300 */
    }
  </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-sans">

  <header class="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-3 flex justify-between items-center">
      <h1 class="text-2xl font-bold">
        <span class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          Teacher Program Diary
        </span>
      </h1>
      <div class="flex items-center space-x-3">
        <button id="darkModeToggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary">
          <svg id="themeIconMoon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 hidden">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
          </svg>
          <svg id="themeIconSun" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-6.364-.386l1.591-1.591M3 12h2.25m.386-6.364l1.591 1.591M12 5.25A6.75 6.75 0 005.25 12a6.75 6.75 0 006.75 6.75s0 0 0 0a6.75 6.75 0 006.75-6.75A6.75 6.75 0 0012 5.25z" />
          </svg>
        </button>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6 mb-6 bg-card">
      <div id="currentDate" class="text-center text-lg font-medium text-gray-700 dark:text-gray-300"></div>
    </div>

    <div id="gradeSelectorSection" class="selector-section active mb-6 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6 bg-card">
      <h2 class="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">Select Grade</h2>
      <div id="gradeButtons" class="flex flex-wrap gap-2">
        <span class="text-gray-500 dark:text-gray-400">Loading grades...</span>
      </div>
      <div class="mt-4">
        <input type="text" id="newGradeName" placeholder="New grade name (e.g., Grade 5)" class="mt-1 block w-full sm:w-1/2 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 p-2 text-sm">
        <button id="addGradeBtn" class="mt-2 px-4 py-2 bg-secondary text-white text-sm font-medium rounded-md hover:bg-violet-600 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50">Add Grade</button>
      </div>
    </div>

    <div id="subjectSelectorSection" class="selector-section mb-6 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6 bg-card">
      <h2 class="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">Select Subject for <span id="selectedGradeForSubjectLabel" class="text-primary dark:text-primary-dark">N/A</span></h2>
      <div id="subjectButtons" class="flex flex-wrap gap-2">
        <span class="text-gray-500 dark:text-gray-400">Select a grade to see subjects.</span>
      </div>
      <div class="mt-4">
        <input type="text" id="newSubjectName" placeholder="New subject name (e.g., Mathematics)" class="mt-1 block w-full sm:w-1/2 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-tertiary focus:ring focus:ring-tertiary focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 p-2 text-sm">
        <button id="addSubjectBtn" class="mt-2 px-4 py-2 bg-tertiary text-white text-sm font-medium rounded-md hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-tertiary focus:ring-opacity-50">Add Subject</button>
      </div>
    </div>

    <div id="entryFormSection" class="selector-section mb-6 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6 bg-card">
      <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
        Add New Topic for <span id="selectedGradeForEntryLabel" class="text-primary dark:text-primary-dark">N/A</span> - <span id="selectedSubjectForEntryLabel" class="text-tertiary dark:text-emerald-400">N/A</span>
      </h2>
      <form id="entryForm">
        <div class="mb-4">
          <label for="plannedDateInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Planned Date</label>
          <input type="date" id="plannedDateInput" required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 p-2">
        </div>
        <div class="mb-4">
          <label for="topicInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Lesson Topic</label>
          <input type="text" id="topicInput" placeholder="Enter lesson topic" required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 p-2">
        </div>
        <div class="mb-4">
          <label for="objectivesInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Objectives of the Lesson</label>
          <textarea id="objectivesInput" rows="3" placeholder="List the learning objectives..." required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 p-2 resize-y"></textarea>
        </div>
        <div class="mb-4">
          <label for="reviewQuestionsInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Review Questions</label>
          <textarea id="reviewQuestionsInput" rows="3" placeholder="Add some review questions..." required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 p-2 resize-y"></textarea>
        </div>
        <input type="hidden" id="editingEntryId">
        <button type="submit" id="addEntryBtn" class="px-6 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 disabled:opacity-50">
          Add Topic
        </button>
         <button type="button" id="cancelEditBtn" class="px-6 py-2 bg-gray-500 text-white font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 ml-2 hidden">
          Cancel Edit
        </button>
      </form>
    </div>

    <div id="entriesDisplaySection" class="selector-section bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6 bg-card mb-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
        Lesson Topics for <span id="selectedGradeForListLabel" class="text-primary dark:text-primary-dark">N/A</span> - <span id="selectedSubjectForListLabel" class="text-tertiary dark:text-emerald-400">N/A</span>
      </h2>
      <ul id="entryList" class="divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto">
        <li class="p-4 text-center text-gray-500 dark:text-gray-400">Select a grade and subject to see topics.</li>
      </ul>
    </div>

    <div id="calendarViewSection" class="selector-section active always-active bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6 bg-card">
        <div class="flex justify-between items-center mb-4">
            <button id="prevMonthBtn" class="px-3 py-1.5 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">&lt; Prev</button>
            <h2 id="calendarMonthYear" class="text-xl font-semibold text-gray-800 dark:text-gray-200">Month Year</h2>
            <button id="nextMonthBtn" class="px-3 py-1.5 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">Next &gt;</button>
        </div>
        <table id="calendarTable" class="calendar-table">
            <thead>
                <tr>
                    <th>Sun</th><th>Mon</th><th>Tue</th><th>Wed</th><th>Thu</th><th>Fri</th><th>Sat</th>
                </tr>
            </thead>
            <tbody id="calendarBody">
                </tbody>
        </table>
    </div>

  </div>

  <footer class="text-center py-6 mt-8 border-t border-gray-200 dark:border-gray-700">
    <p class="text-sm text-gray-600 dark:text-gray-400">
      Teacher Program Diary &copy; <span id="year"></span> | Dates & Calendar Version
    </p>
  </footer>

  <div id="deleteConfirmationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center hidden z-50">
    <div class="relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white dark:bg-gray-800">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-800">
          <svg class="h-6 w-6 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
        </div>
        <h3 id="deleteModalTitle" class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mt-2">Delete Item</h3>
        <div class="mt-2 px-7 py-3">
          <p id="deleteModalMessage" class="text-sm text-gray-500 dark:text-gray-400">Are you sure? This action cannot be undone.</p>
        </div>
        <div class="items-center px-4 py-3 space-x-2">
          <button id="confirmDeleteBtn" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-auto sm:w-auto hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            Delete
          </button>
          <button id="cancelDeleteBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-base font-medium rounded-md w-auto sm:w-auto hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>

  <div id="calendarDateDetailModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 overflow-y-auto h-full w-full flex items-center justify-center hidden z-[60]">
        <div class="relative mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-900">
            <div class="flex justify-between items-center mb-3">
                <h3 id="calendarModalDateTitle" class="text-xl font-semibold text-gray-900 dark:text-gray-100">Topics for Date</h3>
                <button id="closeCalendarModalBtn" class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-600 dark:text-gray-300">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div id="calendarModalContent" class="max-h-[60vh] overflow-y-auto text-sm text-gray-700 dark:text-gray-300">
                </div>
        </div>
    </div>


  <div id="toastNotification" class="fixed bottom-5 right-5 bg-green-500 text-white py-2 px-4 rounded-lg shadow-md hidden transition-opacity duration-300 z-[70]">
    <span id="toastMessage"></span>
  </div>

  <script>
    // --- Local Storage Keys ---
    const LS_GRADES_KEY = 'teacherDiaryGrades';
    const LS_SUBJECTS_PREFIX = 'teacherDiarySubjects_'; // + gradeId
    const LS_ENTRIES_PREFIX = 'teacherDiaryEntries_';   // + subjectId

    // --- UI Elements ---
    const currentDateDisplay = document.getElementById("currentDate");
    const yearDisplay = document.getElementById("year");
    const darkModeToggle = document.getElementById("darkModeToggle");
    const themeIconMoon = document.getElementById("themeIconMoon");
    const themeIconSun = document.getElementById("themeIconSun");
    
    const gradeButtonsContainer = document.getElementById("gradeButtons");
    const newGradeNameInput = document.getElementById("newGradeName");
    const addGradeBtn = document.getElementById("addGradeBtn");
    const selectedGradeForSubjectLabel = document.getElementById("selectedGradeForSubjectLabel");
    const selectedGradeForEntryLabel = document.getElementById("selectedGradeForEntryLabel");
    const selectedGradeForListLabel = document.getElementById("selectedGradeForListLabel");

    const subjectSelectorSection = document.getElementById("subjectSelectorSection");
    const subjectButtonsContainer = document.getElementById("subjectButtons");
    const newSubjectNameInput = document.getElementById("newSubjectName");
    const addSubjectBtn = document.getElementById("addSubjectBtn");
    const selectedSubjectForEntryLabel = document.getElementById("selectedSubjectForEntryLabel");
    const selectedSubjectForListLabel = document.getElementById("selectedSubjectForListLabel");

    const entryFormSection = document.getElementById("entryFormSection");
    const entryForm = document.getElementById("entryForm");
    const plannedDateInput = document.getElementById("plannedDateInput");
    const topicInput = document.getElementById("topicInput");
    const objectivesInput = document.getElementById("objectivesInput");
    const reviewQuestionsInput = document.getElementById("reviewQuestionsInput");
    const addEntryBtn = document.getElementById("addEntryBtn");
    const editingEntryIdInput = document.getElementById("editingEntryId");
    const cancelEditBtn = document.getElementById("cancelEditBtn");

    const entriesDisplaySection = document.getElementById("entriesDisplaySection");
    const entryListEl = document.getElementById("entryList");

    const deleteModal = document.getElementById('deleteConfirmationModal');
    const deleteModalTitle = document.getElementById('deleteModalTitle');
    const deleteModalMessage = document.getElementById('deleteModalMessage');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    let itemToDelete = { type: null, id: null, parentId: null }; 

    const toastNotification = document.getElementById('toastNotification');
    const toastMessage = document.getElementById('toastMessage');

    // Calendar UI
    const calendarMonthYearEl = document.getElementById('calendarMonthYear');
    const calendarBodyEl = document.getElementById('calendarBody');
    const prevMonthBtn = document.getElementById('prevMonthBtn');
    const nextMonthBtn = document.getElementById('nextMonthBtn');
    const calendarDateDetailModal = document.getElementById('calendarDateDetailModal');
    const calendarModalDateTitle = document.getElementById('calendarModalDateTitle');
    const calendarModalContent = document.getElementById('calendarModalContent');
    const closeCalendarModalBtn = document.getElementById('closeCalendarModalBtn');


    // --- App State ---
    let currentGradeId = null; 
    let currentGradeName = null;
    let currentSubjectId = null;
    let currentSubjectName = null;

    let grades = []; 
    let subjectsForCurrentGrade = [];
    let calendarDate = new Date(); // For calendar navigation
    
    // --- Dark Mode ---
    function applyTheme(isDark) {
        console.log("[Theme] Applying theme. Dark mode:", isDark);
        document.documentElement.classList.toggle('dark', isDark);
        themeIconMoon.classList.toggle('hidden', !isDark);
        themeIconSun.classList.toggle('hidden', isDark);

        const root = document.documentElement;
        const defaultDarkHighlight = '#7c2d12'; 
        const defaultLightHighlight = '#fde68a';
        let darkHighlight = defaultDarkHighlight;
        let lightHighlight = defaultLightHighlight;

        if (typeof tailwind !== 'undefined' && tailwind.config && tailwind.config.theme && tailwind.config.theme.extend && tailwind.config.theme.extend.colors) {
            const twColors = tailwind.config.theme.extend.colors;
            darkHighlight = twColors.darkCalendarHighlightColor || defaultDarkHighlight;
            lightHighlight = twColors.calendarHighlightColor || defaultLightHighlight;
            console.log("[Theme] Using Tailwind colors for calendar highlights.");
        } else {
            console.warn("[Theme] Tailwind config colors not available for calendar. Using defaults.");
        }
        
        root.style.setProperty('--calendar-highlight-color', isDark ? darkHighlight : lightHighlight);
        
        if (typeof renderCalendar === "function") {
            try {
                renderCalendar();
            } catch (e) {
                console.error("[Error] Error during renderCalendar called from applyTheme:", e);
            }
        } else {
            console.warn("[Theme] renderCalendar function not defined when applyTheme was called.");
        }
    }

    darkModeToggle.addEventListener("click", () => {
      const isDark = document.documentElement.classList.toggle("dark");
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
      applyTheme(isDark);
    });

    // --- Date & Time ---
    function updateCurrentDate() {
      const today = new Date();
      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
      currentDateDisplay.textContent = today.toLocaleDateString(undefined, options);
    }
     function getISODateString(date) { 
        if (!(date instanceof Date) || isNaN(date)) { 
            console.warn("[Util] getISODateString received invalid date:", date, "Defaulting to today.");
            date = new Date();
        }
        return date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0' + date.getDate()).slice(-2);
    }


    // --- Toast Notifications ---
    function showToast(message, type = 'success') {
        toastMessage.textContent = message;
        toastNotification.className = `fixed bottom-5 right-5 text-white py-2 px-4 rounded-lg shadow-md transition-opacity duration-300 z-[70] ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
        toastNotification.classList.remove('opacity-0');
        toastNotification.classList.add('opacity-100');
        setTimeout(() => {
            toastNotification.classList.remove('opacity-100');
            toastNotification.classList.add('opacity-0');
            setTimeout(() => toastNotification.classList.add('hidden'), 300);
        }, 3000);
    }

    // --- Local Storage Data Functions ---
    function getFromLocalStorage(key, defaultValue = []) {
        const json = localStorage.getItem(key);
        try {
            return json ? JSON.parse(json) : defaultValue;
        } catch (e) {
            console.error("[LS Error] Error parsing JSON from localStorage for key:", key, e);
            return defaultValue;
        }
    }
    function saveToLocalStorage(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
        if (typeof renderCalendar === "function") {
            try {
                renderCalendar(); 
            } catch (e) {
                console.error("[Error] Error during renderCalendar called from saveToLocalStorage:", e);
            }
        }
    }

    // --- UI State Management ---
    function updateSectionVisibility() {
        console.log(`[UI Update] updateSectionVisibility called. currentGradeId: ${currentGradeId}, currentSubjectId: ${currentSubjectId}`);
        subjectSelectorSection.classList.toggle('active', !!currentGradeId);
        entryFormSection.classList.toggle('active', !!currentSubjectId);
        entriesDisplaySection.classList.toggle('active', !!currentSubjectId);
        console.log(`[UI Update] Subject section active: ${subjectSelectorSection.classList.contains('active')}`);
        console.log(`[UI Update] Entry form section active: ${entryFormSection.classList.contains('active')}`);
    }

    // --- Grade Logic ---
    function loadGradesAndInitialize() {
        console.log("[Init] loadGradesAndInitialize starting...");
        grades = getFromLocalStorage(LS_GRADES_KEY);
        grades.sort((a, b) => a.name.localeCompare(b.name));
        populateGradeButtons();
        
        const lastSelectedGradeId = localStorage.getItem('lastSelectedGradeId');
        const gradeToSelect = grades.find(g => g.id === lastSelectedGradeId);

        if (gradeToSelect) {
            console.log("[Init] Restoring last selected grade:", gradeToSelect.name);
            selectGrade(gradeToSelect.id, gradeToSelect.name);
        } else if (grades.length > 0) { 
            console.log("[Init] No last selected grade found, selecting first grade:", grades[0].name);
            selectGrade(grades[0].id, grades[0].name);
        } else { 
             console.log("[Init] No grades found in local storage.");
             subjectButtonsContainer.innerHTML = '<span class="text-gray-500 dark:text-gray-400">Add a grade to get started.</span>';
             entryListEl.innerHTML = '<li class="p-4 text-center text-gray-500 dark:text-gray-400">Add a grade and subject to begin.</li>';
             currentGradeId = null; 
             currentSubjectId = null; 
        }
        updateSectionVisibility(); 
        if (typeof renderCalendar === "function") {
            try {
                renderCalendar(); 
            } catch (e) {
                console.error("[Error] Error during renderCalendar called from loadGradesAndInitialize:", e);
            }
        }
        console.log("[Init] loadGradesAndInitialize finished.");
    }

    function populateGradeButtons() {
        console.log("[UI Update] populateGradeButtons called. Current active gradeId:", currentGradeId);
        gradeButtonsContainer.innerHTML = ""; 
        if (grades.length === 0) {
            gradeButtonsContainer.innerHTML = '<span class="text-gray-500 dark:text-gray-400">No grades found. Add one below.</span>';
            return;
        }
        grades.forEach(grade => {
            const btn = document.createElement("button");
            btn.textContent = grade.name;
            btn.dataset.gradeId = grade.id;
            btn.className = `px-3 py-1.5 text-sm font-medium rounded-full border transition-colors duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-opacity-50`;
            const isActive = grade.id === currentGradeId;
            
            ["bg-primary", "text-white", "border-primary", "focus:ring-primary",
             "bg-gray-200", "dark:bg-gray-700", "text-gray-700", "dark:text-gray-300",
             "border-gray-300", "dark:border-gray-600", "focus:ring-gray-400"].forEach(cls => btn.classList.remove(cls));

            if (isActive) {
                btn.classList.add("bg-primary", "text-white", "border-primary", "focus:ring-primary");
            } else {
                btn.classList.add("bg-gray-200", "dark:bg-gray-700", "text-gray-700", "dark:text-gray-300",
                                "border-gray-300", "dark:border-gray-600", "focus:ring-gray-400");
            }
            btn.addEventListener("click", () => selectGrade(grade.id, grade.name));
            gradeButtonsContainer.appendChild(btn);
        });
    }
    
    function selectGrade(gradeId, gradeName) {
        console.log(`[Action] selectGrade called. ID: ${gradeId}, Name: ${gradeName}`);
        currentGradeId = gradeId;
        currentGradeName = gradeName;
        localStorage.setItem('lastSelectedGradeId', gradeId);

        selectedGradeForSubjectLabel.textContent = gradeName;
        selectedGradeForEntryLabel.textContent = gradeName;
        selectedGradeForListLabel.textContent = gradeName;

        currentSubjectId = null; 
        currentSubjectName = null;
        selectedSubjectForEntryLabel.textContent = "N/A";
        selectedSubjectForListLabel.textContent = "N/A";
        entryListEl.innerHTML = '<li class="p-4 text-center text-gray-500 dark:text-gray-400">Select a subject.</li>';
        
        populateGradeButtons(); 
        loadSubjectsForGrade(gradeId); 
        updateSectionVisibility();
    }

    function handleAddGrade() {
        console.log("[Action] handleAddGrade called.");
        const gradeName = newGradeNameInput.value.trim();
        if (!gradeName) {
            showToast("Grade name cannot be empty.", "error");
            console.log("[handleAddGrade] Grade name empty.");
            return;
        }
        if (grades.find(g => g.name.toLowerCase() === gradeName.toLowerCase())) {
            showToast(`Grade "${gradeName}" already exists.`, "error");
            console.log("[handleAddGrade] Grade already exists.");
            return;
        }

        const newGrade = { id: Date.now().toString(), name: gradeName, createdAt: new Date().toISOString() };
        grades.push(newGrade);
        grades.sort((a, b) => a.name.localeCompare(b.name));
        saveToLocalStorage(LS_GRADES_KEY, grades);
        
        newGradeNameInput.value = "";
        showToast(`Grade "${gradeName}" added.`, "success");
        console.log("[handleAddGrade] Grade added:", newGrade);
        populateGradeButtons();
        selectGrade(newGrade.id, newGrade.name);
    }

    // --- Subject Logic ---
    function loadSubjectsForGrade(gradeId) {
        console.log(`[Data] loadSubjectsForGrade called for gradeId: ${gradeId}`);
        subjectsForCurrentGrade = getFromLocalStorage(LS_SUBJECTS_PREFIX + gradeId);
        subjectsForCurrentGrade.sort((a, b) => a.name.localeCompare(b.name));
        populateSubjectButtons();

        const lastSelectedSubjectId = localStorage.getItem('lastSelectedSubjectId_' + gradeId);
        const subjectToSelect = subjectsForCurrentGrade.find(s => s.id === lastSelectedSubjectId);

        if (subjectToSelect) {
            console.log(`[Data] Restoring last selected subject for grade ${gradeId}:`, subjectToSelect.name);
            selectSubject(subjectToSelect.id, subjectToSelect.name);
        } else if (subjectsForCurrentGrade.length > 0) {
            console.log(`[Data] No last selected subject, selecting first for grade ${gradeId}:`, subjectsForCurrentGrade[0].name);
            selectSubject(subjectsForCurrentGrade[0].id, subjectsForCurrentGrade[0].name);
        } else { 
            console.log(`[Data] No subjects found for grade ${gradeId}.`);
            currentSubjectId = null;
            currentSubjectName = null;
            selectedSubjectForEntryLabel.textContent = "N/A";
            selectedSubjectForListLabel.textContent = "N/A";
            entryListEl.innerHTML = '<li class="p-4 text-center text-gray-500 dark:text-gray-400">No subjects for this grade. Add one.</li>';
            updateSectionVisibility(); 
        }
    }

    function populateSubjectButtons() {
        console.log("[UI Update] populateSubjectButtons called. Current active subjectId:", currentSubjectId);
        subjectButtonsContainer.innerHTML = "";
        if (!currentGradeId) {
            subjectButtonsContainer.innerHTML = '<span class="text-gray-500 dark:text-gray-400">Select a grade first.</span>';
            return;
        }
        if (subjectsForCurrentGrade.length === 0) {
            subjectButtonsContainer.innerHTML = '<span class="text-gray-500 dark:text-gray-400">No subjects found. Add one below.</span>';
            return;
        }
        subjectsForCurrentGrade.forEach(subject => {
            const btn = document.createElement("button");
            btn.textContent = subject.name;
            btn.dataset.subjectId = subject.id;
            btn.className = `px-3 py-1.5 text-sm font-medium rounded-full border transition-colors duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-opacity-50`;
            const isActive = subject.id === currentSubjectId;

            ["bg-tertiary", "text-white", "border-tertiary", "focus:ring-tertiary",
             "bg-gray-200", "dark:bg-gray-700", "text-gray-700", "dark:text-gray-300",
             "border-gray-300", "dark:border-gray-600", "focus:ring-gray-400"].forEach(cls => btn.classList.remove(cls));

            if (isActive) {
                btn.classList.add("bg-tertiary", "text-white", "border-tertiary", "focus:ring-tertiary");
            } else {
                btn.classList.add("bg-gray-200", "dark:bg-gray-700", "text-gray-700", "dark:text-gray-300",
                                "border-gray-300", "dark:border-gray-600", "focus:ring-gray-400");
            }
            btn.addEventListener("click", () => selectSubject(subject.id, subject.name));
            subjectButtonsContainer.appendChild(btn);
        });
    }

    function selectSubject(subjectId, subjectName) {
        console.log(`[Action] selectSubject called. ID: ${subjectId}, Name: ${subjectName}`);
        currentSubjectId = subjectId;
        currentSubjectName = subjectName;
        if (currentGradeId) { 
            localStorage.setItem('lastSelectedSubjectId_' + currentGradeId, subjectId);
        }

        selectedSubjectForEntryLabel.textContent = subjectName;
        selectedSubjectForListLabel.textContent = subjectName;
        plannedDateInput.value = getISODateString(new Date()); 

        populateSubjectButtons(); 
        loadEntriesForSubject(subjectId);
        updateSectionVisibility();
    }

    function handleAddSubject() {
        console.log("[Action] handleAddSubject called.");
        if (!currentGradeId) return showToast("Please select a grade first.", "error");
        const subjectName = newSubjectNameInput.value.trim();
        if (!subjectName) return showToast("Subject name cannot be empty.", "error");

        subjectsForCurrentGrade = getFromLocalStorage(LS_SUBJECTS_PREFIX + currentGradeId); // Ensure we have the latest list
        if (subjectsForCurrentGrade.find(s => s.name.toLowerCase() === subjectName.toLowerCase())) return showToast(`Subject "${subjectName}" already exists in this grade.`, "error");

        const newSubject = { id: Date.now().toString(), name: subjectName, createdAt: new Date().toISOString() };
        subjectsForCurrentGrade.push(newSubject);
        subjectsForCurrentGrade.sort((a, b) => a.name.localeCompare(b.name));
        saveToLocalStorage(LS_SUBJECTS_PREFIX + currentGradeId, subjectsForCurrentGrade);

        newSubjectNameInput.value = "";
        showToast(`Subject "${subjectName}" added to ${currentGradeName}.`, "success");
        populateSubjectButtons();
        selectSubject(newSubject.id, newSubject.name);
    }

    // --- Entry (Lesson Topic) Logic ---
    function loadEntriesForSubject(subjectId) {
        console.log(`[Data] loadEntriesForSubject called for subjectId: ${subjectId}`);
        if (!subjectId) {
            entryListEl.innerHTML = '<li class="p-4 text-center text-gray-500 dark:text-gray-400">Select a subject to see topics.</li>';
            return;
        }
        const entries = getFromLocalStorage(LS_ENTRIES_PREFIX + subjectId);
        entries.sort((a, b) => {
            const dateA = new Date(a.plannedDate + 'T00:00:00'); 
            const dateB = new Date(b.plannedDate + 'T00:00:00');
            if (dateA.getTime() !== dateB.getTime()) {
                return dateB - dateA; 
            }
            return new Date(b.timestamp) - new Date(a.timestamp); 
        });
        renderEntries(entries);
    }

    function renderEntries(entries) {
        entryListEl.innerHTML = ""; 
        if (entries.length === 0) {
            entryListEl.innerHTML = '<li class="p-4 text-center text-gray-500 dark:text-gray-400">No topics yet for this subject.</li>';
            return;
        }
        entries.forEach(entry => {
            const li = document.createElement("li");
            li.className = "p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150 ease-in-out";
            const plannedDateFormatted = entry.plannedDate ? new Date(entry.plannedDate + 'T00:00:00').toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric'}) : 'Date N/A';
            const entryTime = new Date(entry.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit'});
            li.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                            Planned: <span class="font-medium">${plannedDateFormatted}</span> | Added: ${entryTime}
                        </div>
                        <h3 class="text-lg font-semibold text-primary dark:text-primary-dark">${entry.topic || 'No Topic'}</h3>
                    </div>
                    <div class="flex space-x-2">
                        <button data-id="${entry.id}" class="edit-entry-btn p-1.5 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 rounded-full hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" /></svg>
                        </button>
                        <button data-id="${entry.id}" class="delete-entry-btn p-1.5 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 rounded-full hover:bg-red-100 dark:hover:bg-red-800 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.243.096 3.288.255A1.037 1.037 0 017.38 7.622l.043.105A48.354 48.354 0 0012 7.5c0 .876.126 1.727.356 2.546Zm9.303-3.21c-1.153 0-2.243.096-3.288.255m0 0A1.037 1.037 0 0016.62 7.622l.043.105A48.354 48.354 0 0112 7.5c0 .876.126 1.727.356 2.546M12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5z" /></svg>
                        </button>
                    </div>
                </div>
                <p class="text-sm text-gray-700 dark:text-gray-300 mt-1 whitespace-pre-wrap"><strong>Objectives:</strong> ${entry.objectives || 'N/A'}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 whitespace-pre-wrap"><strong>Review:</strong> ${entry.review || 'N/A'}</p>
            `;
            entryListEl.appendChild(li);
            li.querySelector('.delete-entry-btn').addEventListener('click', (e) => {
                itemToDelete = { type: 'entry', id: e.currentTarget.dataset.id, parentId: currentSubjectId };
                deleteModalTitle.textContent = "Delete Topic";
                deleteModalMessage.textContent = "Are you sure you want to delete this lesson topic? This action cannot be undone.";
                deleteModal.classList.remove('hidden');
            });
            li.querySelector('.edit-entry-btn').addEventListener('click', (e) => {
                const entryId = e.currentTarget.dataset.id;
                const entriesForCurrentSubject = getFromLocalStorage(LS_ENTRIES_PREFIX + currentSubjectId);
                const entryToEdit = entriesForCurrentSubject.find(en => en.id === entryId);
                if (entryToEdit) populateFormForEdit(entryToEdit);
            });
        });
    }

    function populateFormForEdit(entry) {
        plannedDateInput.value = entry.plannedDate || getISODateString(new Date());
        topicInput.value = entry.topic;
        objectivesInput.value = entry.objectives;
        reviewQuestionsInput.value = entry.review;
        editingEntryIdInput.value = entry.id;
        addEntryBtn.textContent = "Update Topic";
        cancelEditBtn.classList.remove("hidden");
        topicInput.focus();
        entryForm.scrollIntoView({ behavior: 'smooth' });
    }

    function resetForm() {
        entryForm.reset();
        plannedDateInput.value = getISODateString(new Date()); 
        editingEntryIdInput.value = "";
        addEntryBtn.textContent = "Add Topic";
        addEntryBtn.disabled = false; 
        cancelEditBtn.classList.add("hidden");
    }

    function handleFormSubmit(event) {
        event.preventDefault();
        if (!currentSubjectId) return showToast("Please select a grade and subject first.", "error");

        const plannedDate = plannedDateInput.value;
        const topic = topicInput.value.trim();
        const objectives = objectivesInput.value.trim();
        const review = reviewQuestionsInput.value.trim();
        if (!plannedDate || !topic || !objectives || !review) return showToast("All fields are required, including planned date.", "error");
        
        addEntryBtn.disabled = true;
        const entryIdToUpdate = editingEntryIdInput.value;
        let currentEntries = getFromLocalStorage(LS_ENTRIES_PREFIX + currentSubjectId);
        const timestamp = new Date().toISOString();

        if (entryIdToUpdate) {
            currentEntries = currentEntries.map(entry => 
                entry.id === entryIdToUpdate 
                ? { ...entry, plannedDate, topic, objectives, review, timestamp } 
                : entry
            );
            showToast("Topic updated successfully!", "success");
        } else {
            currentEntries.push({ id: Date.now().toString(), plannedDate, topic, objectives, review, timestamp });
            showToast("Topic added successfully!", "success");
        }
        
        saveToLocalStorage(LS_ENTRIES_PREFIX + currentSubjectId, currentEntries); 
        resetForm();
        loadEntriesForSubject(currentSubjectId);
        addEntryBtn.disabled = false; 
    }

    function handleDeleteConfirmed() {
        if (!itemToDelete.type || !itemToDelete.id) return;

        if (itemToDelete.type === 'entry') {
            let entries = getFromLocalStorage(LS_ENTRIES_PREFIX + itemToDelete.parentId); 
            entries = entries.filter(entry => entry.id !== itemToDelete.id);
            saveToLocalStorage(LS_ENTRIES_PREFIX + itemToDelete.parentId, entries); 
            showToast("Topic deleted successfully.", "success");
            loadEntriesForSubject(itemToDelete.parentId);
        }
        
        deleteModal.classList.add('hidden');
        itemToDelete = { type: null, id: null, parentId: null };
    }

    // --- Calendar Logic ---
    function getAllEntriesWithDetails() {
        const allEntries = [];
        const currentGrades = getFromLocalStorage(LS_GRADES_KEY);
        currentGrades.forEach(grade => {
            const subjects = getFromLocalStorage(LS_SUBJECTS_PREFIX + grade.id);
            subjects.forEach(subject => {
                const entries = getFromLocalStorage(LS_ENTRIES_PREFIX + subject.id);
                entries.forEach(entry => {
                    if (entry.plannedDate) {
                        allEntries.push({
                            ...entry,
                            gradeName: grade.name,
                            subjectName: subject.name
                        });
                    }
                });
            });
        });
        return allEntries;
    }


    function renderCalendar() {
        const year = calendarDate.getFullYear();
        const month = calendarDate.getMonth(); 

        calendarMonthYearEl.textContent = `${calendarDate.toLocaleString('default', { month: 'long' })} ${year}`;
        calendarBodyEl.innerHTML = '';

        const firstDayOfMonth = new Date(year, month, 1).getDay(); 
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        const allEntriesWithDetails = getAllEntriesWithDetails();
        const entriesByDate = {};
        allEntriesWithDetails.forEach(entry => {
            if (!entriesByDate[entry.plannedDate]) {
                entriesByDate[entry.plannedDate] = [];
            }
            entriesByDate[entry.plannedDate].push(entry);
        });

        let date = 1;
        for (let i = 0; i < 6; i++) { 
            const row = document.createElement('tr');
            for (let j = 0; j < 7; j++) {
                const cell = document.createElement('td');
                // cell.className = "p-2 h-24"; // Tailwind classes for padding and height
                if (i === 0 && j < firstDayOfMonth) {
                    cell.classList.add('other-month');
                } else if (date > daysInMonth) {
                    cell.classList.add('other-month');
                } else {
                    const cellDateISO = getISODateString(new Date(year, month, date));
                    const dayNumberDiv = document.createElement('div');
                    dayNumberDiv.className = 'calendar-day-number';
                    dayNumberDiv.textContent = date;
                    cell.appendChild(dayNumberDiv);
                    cell.dataset.date = cellDateISO;

                    if (cellDateISO === getISODateString(new Date())) {
                        cell.classList.add('current-day');
                    }
                    if (entriesByDate[cellDateISO] && entriesByDate[cellDateISO].length > 0) {
                        cell.classList.add('has-entries');
                        const indicator = document.createElement('span');
                        indicator.className = 'calendar-entry-indicator';
                        indicator.textContent = `${entriesByDate[cellDateISO].length} topic(s)`;
                        cell.appendChild(indicator);
                        cell.addEventListener('click', () => showCalendarDateDetails(cellDateISO, entriesByDate[cellDateISO]));
                    }
                    date++;
                }
                row.appendChild(cell);
            }
            calendarBodyEl.appendChild(row);
            if (date > daysInMonth && i < 5) { 
                let allCellsFilled = true;
                Array.from(row.children).forEach(childCell => {
                    if (!childCell.classList.contains('other-month') && childCell.textContent === '') {
                        allCellsFilled = false;
                    }
                });
                if(allCellsFilled && date > daysInMonth) break;
            } else if (date > daysInMonth && i === 5) { 
                 break;
            }
        }
    }

    function showCalendarDateDetails(dateISO, entries) {
        const dateObj = new Date(dateISO + 'T00:00:00'); 
        calendarModalDateTitle.textContent = `Topics for ${dateObj.toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}`;
        calendarModalContent.innerHTML = '';

        if (!entries || entries.length === 0) {
            calendarModalContent.innerHTML = '<p>No topics planned for this date.</p>';
        } else {
            const ul = document.createElement('ul');
            ul.className = 'space-y-3';
            entries.sort((a,b) => a.gradeName.localeCompare(b.gradeName) || a.subjectName.localeCompare(b.subjectName) || a.topic.localeCompare(b.topic));
            
            entries.forEach(entry => {
                const li = document.createElement('li');
                li.className = 'p-2 border-b dark:border-gray-700';
                li.innerHTML = `
                    <p class="font-semibold text-primary dark:text-primary-dark">${entry.topic}</p>
                    <p class="text-xs text-gray-600 dark:text-gray-400">Grade: ${entry.gradeName} | Subject: ${entry.subjectName}</p>
                    <p class="mt-1 text-xs whitespace-pre-wrap">Objectives: ${entry.objectives}</p>
                `;
                ul.appendChild(li);
            });
            calendarModalContent.appendChild(ul);
        }
        calendarDateDetailModal.classList.remove('hidden');
    }
    
    prevMonthBtn.addEventListener("click", () => {
        calendarDate.setMonth(calendarDate.getMonth() - 1);
        if (typeof renderCalendar === "function") renderCalendar();
    });

    nextMonthBtn.addEventListener("click", () => {
        calendarDate.setMonth(calendarDate.getMonth() + 1);
        if (typeof renderCalendar === "function") renderCalendar();
    });
    
    if(closeCalendarModalBtn) { // Ensure button exists before adding listener
        closeCalendarModalBtn.addEventListener('click', () => {
            calendarDateDetailModal.classList.add('hidden');
        });
    }


    console.log("[DEBUG] About to attach event listeners...");

    // --- Event Listeners ---
    if (addGradeBtn) {
        addGradeBtn.addEventListener("click", handleAddGrade);
    } else {
        console.error("[Error] addGradeBtn not found!");
    }
    if (addSubjectBtn) {
        addSubjectBtn.addEventListener("click", handleAddSubject);
    } else {
        console.error("[Error] addSubjectBtn not found!");
    }
    if (entryForm) {
        entryForm.addEventListener("submit", handleFormSubmit);
    } else {
        console.error("[Error] entryForm not found!");
    }
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', resetForm);
    } else {
        console.error("[Error] cancelEditBtn not found!");
    }
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', handleDeleteConfirmed);
    } else {
        console.error("[Error] confirmDeleteBtn not found!");
    }
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', () => { 
            if(deleteModal) deleteModal.classList.add('hidden');
        });
    } else {
        console.error("[Error] cancelDeleteBtn not found!");
    }

    console.log("[DEBUG] Event listeners attachment attempt completed.");


    // --- Initialization ---
    window.onload = () => {
      console.log("[Init] window.onload triggered.");
      if(yearDisplay) yearDisplay.textContent = new Date().getFullYear();
      updateCurrentDate(); 
      setInterval(updateCurrentDate, 60000); 

      const storedTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
      applyTheme(storedTheme === 'dark'); 
      
      loadGradesAndInitialize(); 
      if(plannedDateInput) plannedDateInput.value = getISODateString(new Date()); 
      console.log("[Init] Application initialized with Dates & Calendar features.");
    };

  </script>
</body>
</html>
